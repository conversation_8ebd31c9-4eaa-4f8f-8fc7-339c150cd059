.publications {
  background: var(--background-light);
  padding: 6rem 0;
}

/* Publication Stats */
.publication-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 2rem;
  margin-bottom: 4rem;
  padding: 3rem;
  background: var(--gradient-primary);
  border-radius: 20px;
  color: white;
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 3rem;
  font-weight: 700;
  font-family: 'Playfair Display', serif;
  margin-bottom: 0.5rem;
}

.stat-label {
  font-size: 1.1rem;
  font-weight: 500;
  opacity: 0.9;
}

/* Controls */
.publication-controls {
  display: flex;
  flex-wrap: wrap;
  gap: 2rem;
  align-items: center;
  margin-bottom: 3rem;
  padding: 2rem;
  background: white;
  border-radius: 15px;
  box-shadow: var(--shadow-light);
}

.search-box {
  position: relative;
  flex: 1;
  min-width: 300px;
}

.search-icon {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-secondary);
}

.search-input {
  width: 100%;
  padding: 0.75rem 1rem 0.75rem 3rem;
  border: 2px solid var(--border-color);
  border-radius: 25px;
  font-size: 1rem;
  transition: var(--transition-fast);
}

.search-input:focus {
  outline: none;
  border-color: var(--secondary-color);
}

.category-filters {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex-wrap: wrap;
}

.filter-icon {
  color: var(--text-secondary);
  font-size: 1.1rem;
}

.filter-btn {
  padding: 0.5rem 1.5rem;
  border: 2px solid var(--border-color);
  border-radius: 25px;
  background: white;
  color: var(--text-primary);
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition-fast);
}

.filter-btn:hover {
  border-color: var(--secondary-color);
  color: var(--secondary-color);
}

.filter-btn.active {
  background: var(--secondary-color);
  border-color: var(--secondary-color);
  color: white;
}

/* Publications List */
.publications-list {
  display: grid;
  gap: 2rem;
}

.publication-card {
  background: white;
  padding: 2.5rem;
  border-radius: 15px;
  box-shadow: var(--shadow-light);
  border: 1px solid var(--border-color);
  transition: var(--transition-medium);
}

.publication-card:hover {
  box-shadow: var(--shadow-medium);
}

.publication-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.publication-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: var(--gradient-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.2rem;
}

.publication-meta {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.impact-badge {
  padding: 0.3rem 1rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
  text-transform: uppercase;
}

.impact-badge.high-impact {
  background: rgba(39, 174, 96, 0.1);
  color: #27ae60;
}

.impact-badge.medium-impact {
  background: rgba(241, 196, 15, 0.1);
  color: #f1c40f;
}

.impact-badge.low-impact {
  background: rgba(231, 76, 60, 0.1);
  color: #e74c3c;
}

.publication-year {
  color: var(--text-secondary);
  font-weight: 500;
}

.publication-title {
  color: var(--primary-color);
  font-size: 1.4rem;
  margin-bottom: 1rem;
  line-height: 1.3;
}

.publication-authors {
  color: var(--text-secondary);
  margin-bottom: 0.5rem;
  font-weight: 500;
}

.publication-journal {
  color: var(--secondary-color);
  font-style: italic;
  margin-bottom: 1.5rem;
}

.publication-abstract {
  background: var(--background-dark);
  padding: 1.5rem;
  border-radius: 10px;
  margin-bottom: 2rem;
  position: relative;
}

.quote-icon {
  position: absolute;
  top: 1rem;
  left: 1rem;
  color: var(--secondary-color);
  opacity: 0.5;
}

.publication-abstract p {
  margin: 0;
  padding-left: 2rem;
  color: var(--text-secondary);
  line-height: 1.6;
  font-style: italic;
}

.publication-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 1rem;
}

.publication-stats {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.citations {
  color: var(--primary-color);
  font-weight: 600;
}

.doi {
  color: var(--text-secondary);
  font-size: 0.9rem;
  font-family: monospace;
}

.publication-link {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.no-results {
  text-align: center;
  padding: 3rem;
  color: var(--text-secondary);
  font-size: 1.1rem;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .publication-stats {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1.5rem;
    padding: 2.5rem;
  }
  
  .stat-number {
    font-size: 2.5rem;
  }
}

@media (max-width: 768px) {
  .publications {
    padding: 4rem 0;
  }
  
  .publication-stats {
    grid-template-columns: repeat(2, 1fr);
    padding: 2rem;
  }
  
  .stat-number {
    font-size: 2rem;
  }
  
  .publication-controls {
    flex-direction: column;
    align-items: stretch;
    gap: 1.5rem;
    padding: 1.5rem;
  }
  
  .search-box {
    min-width: auto;
  }
  
  .category-filters {
    justify-content: center;
  }
  
  .publication-card {
    padding: 2rem;
  }
  
  .publication-header {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }
  
  .publication-footer {
    flex-direction: column;
    align-items: flex-start;
  }
}

@media (max-width: 480px) {
  .publication-stats {
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    padding: 1.5rem;
  }
  
  .stat-number {
    font-size: 1.8rem;
  }
  
  .stat-label {
    font-size: 1rem;
  }
  
  .publication-controls {
    padding: 1rem;
  }
  
  .category-filters {
    flex-direction: column;
    align-items: stretch;
  }
  
  .filter-btn {
    text-align: center;
  }
  
  .publication-card {
    padding: 1.5rem;
  }
  
  .publication-title {
    font-size: 1.2rem;
  }
  
  .publication-abstract {
    padding: 1rem;
  }
  
  .publication-abstract p {
    padding-left: 1.5rem;
  }
}
