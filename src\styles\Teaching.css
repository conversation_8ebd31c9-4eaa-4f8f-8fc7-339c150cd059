.teaching {
  background: var(--background-dark);
  padding: 6rem 0;
}

/* Teaching Philosophy */
.teaching-philosophy {
  margin-bottom: 4rem;
}

.philosophy-content {
  display: flex;
  align-items: center;
  gap: 3rem;
  background: white;
  padding: 3rem;
  border-radius: 20px;
  box-shadow: var(--shadow-light);
}

.philosophy-icon {
  font-size: 4rem;
  color: var(--secondary-color);
  flex-shrink: 0;
}

.philosophy-text h3 {
  color: var(--primary-color);
  margin-bottom: 1.5rem;
  font-size: 1.8rem;
}

.philosophy-text p {
  color: var(--text-secondary);
  line-height: 1.7;
  font-size: 1.1rem;
  margin: 0;
}

/* Tab Navigation */
.teaching-tabs {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 3rem;
  background: white;
  padding: 1rem;
  border-radius: 15px;
  box-shadow: var(--shadow-light);
}

.tab-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem 2rem;
  border: none;
  background: none;
  color: var(--text-secondary);
  font-weight: 500;
  border-radius: 10px;
  cursor: pointer;
  transition: var(--transition-fast);
}

.tab-btn:hover {
  background: var(--background-dark);
  color: var(--primary-color);
}

.tab-btn.active {
  background: var(--gradient-primary);
  color: white;
}

/* Tab Content */
.tab-content {
  min-height: 400px;
}

/* Courses */
.courses-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
}

.course-card {
  background: white;
  padding: 2.5rem;
  border-radius: 15px;
  box-shadow: var(--shadow-light);
  border: 1px solid var(--border-color);
  transition: var(--transition-medium);
}

.course-card:hover {
  box-shadow: var(--shadow-medium);
}

.course-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.course-code {
  background: var(--gradient-primary);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-weight: 600;
  font-size: 0.9rem;
}

.course-level {
  color: var(--text-secondary);
  font-size: 0.9rem;
  font-weight: 500;
}

.course-title {
  color: var(--primary-color);
  margin-bottom: 1rem;
  font-size: 1.3rem;
}

.course-description {
  color: var(--text-secondary);
  line-height: 1.6;
  margin-bottom: 1.5rem;
}

.course-topics {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 2rem;
}

.topic-tag {
  background: var(--background-dark);
  color: var(--text-primary);
  padding: 0.3rem 0.8rem;
  border-radius: 15px;
  font-size: 0.8rem;
  font-weight: 500;
}

.course-stats {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stat {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--text-secondary);
  font-size: 0.9rem;
}

.rating {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.star {
  color: #ddd;
  font-size: 0.8rem;
}

.star.filled {
  color: #ffd700;
}

.rating-number {
  font-weight: 600;
  color: var(--primary-color);
}

/* Achievements */
.achievements-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.achievement-card {
  background: white;
  padding: 2.5rem;
  border-radius: 15px;
  box-shadow: var(--shadow-light);
  border: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  gap: 2rem;
  transition: var(--transition-medium);
}

.achievement-card:hover {
  box-shadow: var(--shadow-medium);
}

.achievement-icon {
  font-size: 3rem;
  color: var(--secondary-color);
  flex-shrink: 0;
}

.achievement-content h4 {
  color: var(--primary-color);
  margin-bottom: 0.5rem;
  font-size: 1.2rem;
}

.achievement-year {
  color: var(--secondary-color);
  font-weight: 600;
  margin-bottom: 1rem;
}

.achievement-content p {
  color: var(--text-secondary);
  line-height: 1.6;
  margin: 0;
}

/* Testimonials */
.testimonials-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
}

.testimonial-card {
  background: white;
  padding: 2.5rem;
  border-radius: 15px;
  box-shadow: var(--shadow-light);
  border: 1px solid var(--border-color);
  transition: var(--transition-medium);
}

.testimonial-card:hover {
  box-shadow: var(--shadow-medium);
}

.testimonial-rating {
  display: flex;
  gap: 0.3rem;
  margin-bottom: 1.5rem;
}

.testimonial-text {
  color: var(--text-secondary);
  line-height: 1.7;
  font-style: italic;
  margin-bottom: 2rem;
  font-size: 1.1rem;
}

.testimonial-author strong {
  color: var(--primary-color);
  display: block;
  margin-bottom: 0.5rem;
}

.testimonial-course {
  color: var(--text-secondary);
  font-size: 0.9rem;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .philosophy-content {
    gap: 2rem;
    padding: 2.5rem;
  }
  
  .philosophy-icon {
    font-size: 3rem;
  }
  
  .courses-grid,
  .achievements-grid,
  .testimonials-grid {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  }
}

@media (max-width: 768px) {
  .teaching {
    padding: 4rem 0;
  }
  
  .philosophy-content {
    flex-direction: column;
    text-align: center;
    padding: 2rem;
  }
  
  .teaching-tabs {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .tab-btn {
    justify-content: center;
    padding: 0.75rem 1.5rem;
  }
  
  .courses-grid,
  .achievements-grid,
  .testimonials-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
  
  .course-card,
  .achievement-card,
  .testimonial-card {
    padding: 2rem;
  }
  
  .achievement-card {
    flex-direction: column;
    text-align: center;
  }
}

@media (max-width: 480px) {
  .philosophy-content {
    padding: 1.5rem;
  }
  
  .philosophy-text h3 {
    font-size: 1.5rem;
  }
  
  .course-card,
  .achievement-card,
  .testimonial-card {
    padding: 1.5rem;
  }
  
  .course-header {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }
  
  .course-stats {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }
  
  .achievement-icon {
    font-size: 2.5rem;
  }
}
