import React from 'react';
import { motion } from 'framer-motion';
import { FaSun, FaMoon } from 'react-icons/fa';
import { useTheme } from '../contexts/ThemeContext';
import '../styles/ThemeToggle.css';

const ThemeToggle = () => {
  const { isDarkMode, toggleTheme } = useTheme();

  return (
    <motion.button
      className="theme-toggle"
      onClick={toggleTheme}
      whileHover={{ scale: 1.1 }}
      whileTap={{ scale: 0.95 }}
      initial={{ opacity: 0, rotate: -180 }}
      animate={{ opacity: 1, rotate: 0 }}
      transition={{ duration: 0.5 }}
      aria-label={`Switch to ${isDarkMode ? 'light' : 'dark'} mode`}
    >
      <motion.div
        className="toggle-icon-container"
        animate={{ rotate: isDarkMode ? 180 : 0 }}
        transition={{ duration: 0.3, ease: "easeInOut" }}
      >
        {isDarkMode ? (
          <FaSun className="toggle-icon sun-icon" />
        ) : (
          <FaMoon className="toggle-icon moon-icon" />
        )}
      </motion.div>
      
      <motion.div
        className="toggle-background"
        animate={{
          background: isDarkMode 
            ? 'linear-gradient(135deg, #1a237e 0%, #3949ab 100%)'
            : 'linear-gradient(135deg, #ff9800 0%, #f57c00 100%)'
        }}
        transition={{ duration: 0.3 }}
      />
      
      <span className="toggle-tooltip">
        {isDarkMode ? 'Light Mode' : 'Dark Mode'}
      </span>
    </motion.button>
  );
};

export default ThemeToggle;
