.theme-toggle {
  position: fixed;
  top: 100px;
  right: 2rem;
  z-index: 1000;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: var(--shadow-medium);
  transition: all 0.3s ease;
  overflow: hidden;
  background: var(--background-primary);
  border: 2px solid var(--border-color);
}

.theme-toggle:hover {
  box-shadow: var(--shadow-heavy);
  transform: translateY(-2px);
}

.toggle-icon-container {
  position: relative;
  z-index: 2;
  display: flex;
  align-items: center;
  justify-content: center;
}

.toggle-icon {
  font-size: 1.5rem;
  color: var(--text-primary);
  transition: all 0.3s ease;
}

.sun-icon {
  color: #ff9800;
}

.moon-icon {
  color: #64b5f6;
}

.toggle-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  opacity: 0.1;
  transition: all 0.3s ease;
}

.toggle-tooltip {
  position: absolute;
  right: 70px;
  top: 50%;
  transform: translateY(-50%);
  background: var(--background-secondary);
  color: var(--text-primary);
  padding: 0.5rem 1rem;
  border-radius: 25px;
  font-size: 0.9rem;
  font-weight: 500;
  white-space: nowrap;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  box-shadow: var(--shadow-light);
  border: 1px solid var(--border-color);
}

.theme-toggle:hover .toggle-tooltip {
  opacity: 1;
  visibility: visible;
  transform: translateY(-50%) translateX(-5px);
}

.toggle-tooltip::after {
  content: '';
  position: absolute;
  top: 50%;
  right: -6px;
  transform: translateY(-50%);
  width: 0;
  height: 0;
  border-left: 6px solid var(--background-secondary);
  border-top: 6px solid transparent;
  border-bottom: 6px solid transparent;
}

/* Responsive Design */
@media (max-width: 768px) {
  .theme-toggle {
    top: 80px;
    right: 1rem;
    width: 50px;
    height: 50px;
  }
  
  .toggle-icon {
    font-size: 1.2rem;
  }
  
  .toggle-tooltip {
    display: none;
  }
}

@media (max-width: 480px) {
  .theme-toggle {
    top: 70px;
    right: 0.5rem;
    width: 45px;
    height: 45px;
  }
  
  .toggle-icon {
    font-size: 1rem;
  }
}

/* Theme-specific animations */
[data-theme="dark"] .theme-toggle {
  background: var(--background-secondary);
}

[data-theme="light"] .theme-toggle {
  background: var(--background-primary);
}

/* Smooth theme transition for all elements */
* {
  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease !important;
}
