.header {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  z-index: 1000;
  transition: all var(--transition-medium);
}

.header.scrolled {
  background: rgba(255, 255, 255, 0.98);
  box-shadow: var(--shadow-light);
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 0;
}

.logo {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  cursor: pointer;
}

.logo-circle {
  width: 45px;
  height: 45px;
  border-radius: 50%;
  background: var(--gradient-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 700;
  font-size: 0.9rem;
  font-family: 'Playfair Display', serif;
}

.logo-text {
  font-family: 'Playfair Display', serif;
  font-size: 1.4rem;
  font-weight: 600;
  color: var(--primary-color);
}

.nav-desktop {
  display: flex;
}

.nav-list {
  display: flex;
  list-style: none;
  gap: 2rem;
  margin: 0;
  padding: 0;
}

.nav-link {
  position: relative;
  padding: 0.5rem 1rem;
  color: var(--text-primary);
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
  transition: var(--transition-fast);
  border-radius: 25px;
  display: block;
}

.nav-link:hover {
  color: var(--secondary-color);
  background: rgba(52, 152, 219, 0.1);
}

.nav-link.active {
  color: var(--secondary-color);
  background: rgba(52, 152, 219, 0.1);
}

.nav-indicator {
  position: absolute;
  bottom: -2px;
  left: 50%;
  transform: translateX(-50%);
  width: 20px;
  height: 3px;
  background: var(--secondary-color);
  border-radius: 2px;
}

.mobile-menu-toggle {
  display: none;
  background: none;
  border: none;
  font-size: 1.5rem;
  color: var(--primary-color);
  cursor: pointer;
  padding: 0.5rem;
}

.mobile-menu {
  display: none;
  overflow: hidden;
  background: white;
  border-top: 1px solid var(--border-color);
}

.mobile-nav-list {
  list-style: none;
  padding: 1rem 0;
  margin: 0;
}

.mobile-nav-link {
  display: block;
  width: 100%;
  padding: 1rem 2rem;
  color: var(--text-primary);
  font-weight: 500;
  text-decoration: none;
  text-align: left;
  cursor: pointer;
  transition: var(--transition-fast);
}

.mobile-nav-link:hover,
.mobile-nav-link.active {
  background: var(--background-dark);
  color: var(--secondary-color);
}

/* Mobile Styles */
@media (max-width: 768px) {
  .nav-desktop {
    display: none;
  }
  
  .mobile-menu-toggle {
    display: block;
  }
  
  .mobile-menu {
    display: block;
  }
  
  .logo-text {
    font-size: 1.2rem;
  }
  
  .logo-circle {
    width: 40px;
    height: 40px;
    font-size: 0.8rem;
  }
}

@media (max-width: 480px) {
  .header-content {
    padding: 0.75rem 0;
  }
  
  .logo-text {
    display: none;
  }
  
  .mobile-nav-link {
    padding: 0.75rem 1rem;
  }
}
