import React, { createContext, useContext, useState, useEffect } from 'react';

const ThemeContext = createContext();

export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

export const ThemeProvider = ({ children }) => {
  const [isDarkMode, setIsDarkMode] = useState(() => {
    // Check localStorage for saved theme preference
    const savedTheme = localStorage.getItem('theme');
    if (savedTheme) {
      return savedTheme === 'dark';
    }
    // Check system preference
    return window.matchMedia('(prefers-color-scheme: dark)').matches;
  });

  useEffect(() => {
    // Save theme preference to localStorage
    localStorage.setItem('theme', isDarkMode ? 'dark' : 'light');
    
    // Apply theme to document root
    document.documentElement.setAttribute('data-theme', isDarkMode ? 'dark' : 'light');
    
    // Update CSS custom properties
    const root = document.documentElement;
    if (isDarkMode) {
      // Dark mode colors
      root.style.setProperty('--primary-color', '#64b5f6');
      root.style.setProperty('--secondary-color', '#81c784');
      root.style.setProperty('--accent-color', '#ffb74d');
      root.style.setProperty('--background-primary', '#121212');
      root.style.setProperty('--background-secondary', '#1e1e1e');
      root.style.setProperty('--background-light', '#2d2d2d');
      root.style.setProperty('--background-dark', '#0a0a0a');
      root.style.setProperty('--text-primary', '#ffffff');
      root.style.setProperty('--text-secondary', '#b0b0b0');
      root.style.setProperty('--text-muted', '#808080');
      root.style.setProperty('--border-color', '#404040');
      root.style.setProperty('--shadow-light', '0 2px 10px rgba(0, 0, 0, 0.3)');
      root.style.setProperty('--shadow-medium', '0 4px 20px rgba(0, 0, 0, 0.4)');
      root.style.setProperty('--shadow-heavy', '0 8px 30px rgba(0, 0, 0, 0.5)');
      root.style.setProperty('--gradient-primary', 'linear-gradient(135deg, #1a237e 0%, #3949ab 100%)');
      root.style.setProperty('--gradient-secondary', 'linear-gradient(135deg, #2e7d32 0%, #66bb6a 100%)');
    } else {
      // Light mode colors
      root.style.setProperty('--primary-color', '#2196f3');
      root.style.setProperty('--secondary-color', '#4caf50');
      root.style.setProperty('--accent-color', '#ff9800');
      root.style.setProperty('--background-primary', '#ffffff');
      root.style.setProperty('--background-secondary', '#f8f9fa');
      root.style.setProperty('--background-light', '#f5f5f5');
      root.style.setProperty('--background-dark', '#e0e0e0');
      root.style.setProperty('--text-primary', '#333333');
      root.style.setProperty('--text-secondary', '#666666');
      root.style.setProperty('--text-muted', '#999999');
      root.style.setProperty('--border-color', '#e0e0e0');
      root.style.setProperty('--shadow-light', '0 2px 10px rgba(0, 0, 0, 0.1)');
      root.style.setProperty('--shadow-medium', '0 4px 20px rgba(0, 0, 0, 0.15)');
      root.style.setProperty('--shadow-heavy', '0 8px 30px rgba(0, 0, 0, 0.2)');
      root.style.setProperty('--gradient-primary', 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)');
      root.style.setProperty('--gradient-secondary', 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)');
    }
  }, [isDarkMode]);

  const toggleTheme = () => {
    setIsDarkMode(prev => !prev);
  };

  const value = {
    isDarkMode,
    toggleTheme,
    theme: isDarkMode ? 'dark' : 'light'
  };

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  );
};
